#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
Goroutine生命周期管理测试脚本
用于验证Go代码中Goroutine生命周期管理优化的正确性
"""

import os
import sys
import subprocess
import time
from loguru import logger


def check_go_installation():
    """检查Go是否已安装"""
    try:
        result = subprocess.run(['go', 'version'], capture_output=True, text=True)
        if result.returncode == 0:
            logger.info(f"Go版本: {result.stdout.strip()}")
            return True
        else:
            logger.error("Go未安装或不在PATH中")
            return False
    except FileNotFoundError:
        logger.error("Go未安装")
        return False


def analyze_goroutine_improvements():
    """分析Goroutine改进"""
    logger.info("分析Goroutine生命周期管理改进...")
    
    go_file_path = "../ScopeSentry-Scan/internal/task/task.go"
    
    if not os.path.exists(go_file_path):
        logger.error(f"Go文件不存在: {go_file_path}")
        return ["❌ Go文件不存在"]
    
    improvements = []
    
    try:
        with open(go_file_path, 'r', encoding='utf-8') as f:
            content = f.read()
            
        # 检查panic恢复机制
        if 'defer func() {' in content and 'recover()' in content:
            improvements.append("✅ Panic恢复机制已添加")
        else:
            improvements.append("❌ 缺少Panic恢复机制")
        
        # 检查context.WithTimeout使用
        if 'context.WithTimeout' in content:
            improvements.append("✅ Context超时控制已实现")
        else:
            improvements.append("❌ 缺少Context超时控制")
        
        # 检查goroutine取消机制
        if 'contextmanager.GlobalContextManagers.GetContext' in content and 'Done()' in content:
            improvements.append("✅ Goroutine取消机制已实现")
        else:
            improvements.append("❌ 缺少Goroutine取消机制")
        
        # 检查超时日志
        if 'timeout for task' in content:
            improvements.append("✅ 超时日志记录已添加")
        else:
            improvements.append("❌ 缺少超时日志记录")
        
        # 检查资源清理
        if 'defer cancel()' in content:
            improvements.append("✅ 资源清理机制已实现")
        else:
            improvements.append("❌ 缺少资源清理机制")
        
        return improvements
        
    except Exception as e:
        logger.error(f"分析Go文件时出错: {e}")
        return []


def check_goroutine_patterns():
    """检查Goroutine使用模式"""
    logger.info("检查Goroutine使用模式...")
    
    go_file_path = "../ScopeSentry-Scan/internal/task/task.go"
    patterns_found = []
    
    try:
        with open(go_file_path, 'r', encoding='utf-8') as f:
            lines = f.readlines()
        
        goroutine_count = 0
        protected_goroutine_count = 0
        
        for i, line in enumerate(lines):
            if 'go func()' in line:
                goroutine_count += 1
                
                # 检查接下来的几行是否有panic恢复
                has_panic_recovery = False
                has_timeout_control = False
                
                for j in range(i, min(i + 20, len(lines))):
                    if 'recover()' in lines[j]:
                        has_panic_recovery = True
                    if 'context.WithTimeout' in lines[j]:
                        has_timeout_control = True
                
                if has_panic_recovery and has_timeout_control:
                    protected_goroutine_count += 1
                    patterns_found.append(f"✅ 第{i+1}行: 受保护的Goroutine")
                else:
                    patterns_found.append(f"⚠️ 第{i+1}行: 未完全保护的Goroutine")
        
        patterns_found.insert(0, f"总Goroutine数: {goroutine_count}")
        patterns_found.insert(1, f"受保护的Goroutine数: {protected_goroutine_count}")
        patterns_found.insert(2, f"保护率: {(protected_goroutine_count/goroutine_count*100):.1f}%" if goroutine_count > 0 else "保护率: 0%")
        
        return patterns_found
        
    except Exception as e:
        logger.error(f"检查Goroutine模式时出错: {e}")
        return []


def test_go_syntax():
    """测试Go代码语法"""
    logger.info("测试Go代码语法...")
    
    go_project_path = "../ScopeSentry-Scan"
    
    if not os.path.exists(go_project_path):
        logger.error(f"Go项目目录不存在: {go_project_path}")
        return False
    
    try:
        # 切换到Go项目目录
        original_cwd = os.getcwd()
        os.chdir(go_project_path)
        
        # 运行go mod tidy（如果有go.mod文件）
        if os.path.exists("go.mod"):
            logger.info("运行 go mod tidy...")
            result = subprocess.run(['go', 'mod', 'tidy'], capture_output=True, text=True, timeout=60)
            if result.returncode != 0:
                logger.warning(f"go mod tidy警告: {result.stderr}")
        
        # 检查语法
        logger.info("检查Go代码语法...")
        result = subprocess.run(['go', 'build', '-o', '/dev/null', './...'], capture_output=True, text=True, timeout=120)
        
        os.chdir(original_cwd)
        
        if result.returncode == 0:
            logger.info("✅ Go代码语法检查通过")
            return True
        else:
            logger.error(f"❌ Go代码语法错误: {result.stderr}")
            return False
            
    except subprocess.TimeoutExpired:
        logger.error("Go语法检查超时")
        os.chdir(original_cwd)
        return False
    except Exception as e:
        logger.error(f"Go语法检查时出错: {e}")
        os.chdir(original_cwd)
        return False


def generate_improvement_summary():
    """生成改进总结"""
    logger.info("生成Goroutine生命周期管理改进总结...")
    
    summary = {
        "改进内容": [
            "添加了panic恢复机制，防止goroutine崩溃影响整个程序",
            "实现了context.WithTimeout超时控制，防止goroutine无限运行",
            "添加了goroutine取消机制，支持优雅关闭",
            "实现了资源清理机制，确保context正确释放",
            "添加了详细的日志记录，便于监控和调试"
        ],
        "性能改进": [
            "防止goroutine泄漏，减少内存使用",
            "超时控制避免资源长期占用",
            "panic恢复提高系统稳定性",
            "优雅关闭机制提升用户体验"
        ],
        "安全性提升": [
            "防止panic导致的程序崩溃",
            "避免goroutine无限制增长",
            "资源泄漏风险大幅降低",
            "系统稳定性显著提升"
        ]
    }
    
    return summary


def main():
    """主测试函数"""
    logger.info("=" * 60)
    logger.info("Goroutine生命周期管理测试开始")
    logger.info("=" * 60)
    
    # 检查Go安装
    if not check_go_installation():
        logger.warning("Go未安装，跳过语法检查，仅进行代码分析")
        syntax_ok = None
    else:
        syntax_ok = test_go_syntax()
    
    # 分析Goroutine改进
    improvements = analyze_goroutine_improvements()
    
    # 检查Goroutine模式
    patterns = check_goroutine_patterns()
    
    # 生成改进总结
    summary = generate_improvement_summary()
    
    # 输出结果
    logger.info("=" * 60)
    logger.info("测试结果总结:")
    
    if syntax_ok is not None:
        logger.info(f"✅ Go代码语法检查: {'通过' if syntax_ok else '失败'}")
    else:
        logger.info("⚠️ Go代码语法检查: 跳过（Go未安装）")
    
    logger.info("\n📊 Goroutine改进分析:")
    for improvement in improvements:
        logger.info(f"  {improvement}")
    
    logger.info("\n🔍 Goroutine模式检查:")
    for pattern in patterns:
        logger.info(f"  {pattern}")
    
    logger.info("\n📈 改进总结:")
    for category, items in summary.items():
        logger.info(f"  {category}:")
        for item in items:
            logger.info(f"    • {item}")
    
    logger.info("=" * 60)
    
    # 判断整体成功
    success_count = sum(1 for imp in improvements if imp.startswith("✅"))
    total_count = len(improvements)
    
    if success_count >= total_count * 0.8:  # 80%以上通过
        logger.info("🎉 Goroutine生命周期管理优化验证成功！")
    else:
        logger.warning("⚠️ Goroutine生命周期管理优化需要进一步改进")


if __name__ == "__main__":
    main()
