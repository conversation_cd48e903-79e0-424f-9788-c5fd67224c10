# -------------------------------------
# @file      : apscheduler_handler.py
# <AUTHOR> Autumn
# @contact   : <EMAIL>
# @time      : 2024/4/21 19:36
# -------------------------------------------

from apscheduler.schedulers.asyncio import AsyncIOScheduler
from apscheduler.jobstores.mongodb import MongoDBJobStore
from apscheduler.executors.pool import ThreadPoolExecutor
from core.config import *

# MongoDB配置
mongo_config = {
    'host': MONGODB_IP,
    'port': int(MONGODB_PORT),
    'username': str(MONGODB_USER),
    'password': str(MONGODB_PASSWORD),
    'database': str(MONGODB_DATABASE),
    'collection': 'apscheduler'
}

# 决策理由: 配置线程池执行器限制最大工作线程，避免线程争用和资源耗尽
executors = {
    'default': ThreadPoolExecutor(max_workers=10),  # 限制最大工作线程数
}

# 决策理由: 配置job默认参数，防止任务重复执行和资源浪费
job_defaults = {
    'coalesce': True,  # 合并排队的相同job，避免重复执行
    'max_instances': 1,  # 每个job最多同时运行1个实例
    'misfire_grace_time': 30  # 错过执行的容忍时间（秒）
}

# 任务存储配置
jobstores = {
    'mongo': MongoDBJobStore(**mongo_config)
}

# 创建优化后的调度器
scheduler = AsyncIOScheduler(
    jobstores=jobstores,
    executors=executors,
    job_defaults=job_defaults
)
