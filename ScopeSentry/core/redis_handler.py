# -*- coding:utf-8 -*-
# @name: redis_handler
# @auth: <EMAIL>
# @version:
import asyncio
import json
import sys
from collections import deque
from urllib.parse import quote_plus
import redis.asyncio as redis
from redis.asyncio import ConnectionPool

from core.db import *
from core.handler.task import get_task_data
from core.util import *
import socket
from motor.motor_asyncio import AsyncIOMotorCursor


class RedisManager:
    """Redis连接池管理器 - 单例模式实现高效连接复用"""

    def __init__(self):
        self.pool = None
        self._lock = asyncio.Lock()

    async def get_pool(self):
        """获取Redis连接池"""
        if self.pool is None:
            async with self._lock:
                # 双重检查锁定模式
                if self.pool is None:
                    try:
                        # 决策理由: 使用连接池配置优化Redis连接管理，减少连接建立/销毁开销
                        self.pool = ConnectionPool.from_url(
                            f"redis://:{quote_plus(REDIS_PASSWORD)}@{REDIS_IP}:{REDIS_PORT}",
                            max_connections=50,  # 最大连接数
                            retry_on_timeout=True,  # 超时重试
                            health_check_interval=30,  # 健康检查间隔30秒
                            socket_keepalive=True,  # 启用TCP keepalive
                            socket_keepalive_options={
                                socket.TCP_KEEPIDLE: 60,
                                socket.TCP_KEEPINTVL: 10,
                                socket.TCP_KEEPCNT: 10
                            } if hasattr(socket, 'TCP_KEEPIDLE') else {},
                            encoding="utf-8",
                            decode_responses=True
                        )
                        logger.info("Redis连接池初始化成功")
                    except Exception as e:
                        logger.error(f"Redis连接池初始化失败: {e}")
                        raise

        return redis.Redis(connection_pool=self.pool)

    async def close(self):
        """关闭连接池"""
        if self.pool:
            await self.pool.disconnect()
            self.pool = None
            logger.info("Redis连接池已关闭")


# 全局Redis管理器实例
redis_manager = RedisManager()


async def get_redis_pool():
    """获取Redis连接 - 使用连接池优化版本"""
    return await redis_manager.get_pool()


async def refresh_config(name, t, content=None):
    data = {
        "name": name,
        "type": t,
    }
    if content is not None:
        data['content'] = content

    redis_client = await get_redis_pool()
    try:
        name_all = []
        if name == "all":
            keys = await redis_client.keys("node:*")
            for key in keys:
                tmp_name = key.split(":")[1]
                name_all.append(tmp_name)
        else:
            name_all.append(name)
        for n in name_all:
            await redis_client.rpush(f"refresh_config:{n}", json.dumps(data))
    finally:
        # 连接池会自动管理连接，无需手动关闭
        pass


async def subscribe_log_channel():
    """Redis pub/sub日志订阅 - 优化版本，支持错误恢复和连接复用"""
    channel_name = 'logs'
    logger.info(f"Subscribed to channel {channel_name}")
    retry_count = 0
    max_retries = 5

    # 决策理由: 使用固定大小的deque替代列表，防止LOG_INFO内存泄漏
    MAX_LOG_ENTRIES = 1000

    while retry_count < max_retries:
        try:
            redis_client = await redis_manager.get_pool()
            async with redis_client.pubsub() as pubsub:
                await pubsub.psubscribe(channel_name)
                retry_count = 0  # 重置重试计数
                logger.info("Redis pub/sub连接已建立")

                while True:
                    message = await pubsub.get_message(
                        ignore_subscribe_messages=True,
                        timeout=10
                    )
                    if message is not None:
                        try:
                            data = json.loads(message["data"])
                            log_name = data["name"]

                            # 修复内存泄漏：使用固定大小的deque
                            if log_name in GET_LOG_NAME:
                                if log_name not in LOG_INFO:
                                    LOG_INFO[log_name] = deque(maxlen=MAX_LOG_ENTRIES)
                                LOG_INFO[log_name].append(data['log'])

                            if "Register Success" in data['log']:
                                await check_node_task(log_name, redis_client)

                            await redis_client.rpush(f'log:{log_name}', data['log'])
                            total_logs = await redis_client.llen(f'log:{log_name}')
                            if total_logs > TOTAL_LOGS:
                                await redis_client.delete(f'log:{log_name}')

                        except json.JSONDecodeError as e:
                            logger.error(f"JSON解析错误: {e}")
                        except Exception as e:
                            logger.error(f"处理消息时出错: {e}")

        except Exception as e:
            retry_count += 1
            logger.error(f"Pubsub连接错误 (尝试 {retry_count}/{max_retries}): {e}")
            if retry_count < max_retries:
                await asyncio.sleep(min(2 ** retry_count, 30))  # 指数退避
            else:
                logger.error("达到最大重试次数，停止重连")
                break


async def check_node_task(node_name, redis_conn):
    """检查节点任务 - 使用新的连接池管理"""
    mongo_client = await get_mongo_db()
    query = {
        "progress": {"$ne": 100},
        "status": 1,
        "$or": [
            {"node": node_name},
            {"allNode": True}
        ]
    }
    cursor: AsyncIOMotorCursor = mongo_client.task.find(query)
    result = await cursor.to_list(length=None)
    if len(result) == 0:
        return
    # Process the result as needed
    for doc in result:
        template_data = await get_task_data(mongo_client, doc, str(doc["_id"]))
        await redis_conn.rpush(f"NodeTask:{node_name}", json.dumps(template_data))
    return


async def get_redis_online_data(redis_con=None):
    """获取在线节点数据 - 使用连接池优化版本"""
    if redis_con is None:
        redis_con = await get_redis_pool()

    # 获取所有以 node: 开头的键
    keys = await redis_con.keys("node:*")
    # 构建结果字典
    result = []
    for key in keys:
        name = key.split(":")[1]
        hash_data = await redis_con.hgetall(key)
        if hash_data.get('state') == '1':
            result.append(name)
    return result
