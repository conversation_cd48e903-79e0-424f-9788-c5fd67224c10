# -*- coding:utf-8 -*-
# @name: redis_handler
# @auth: <EMAIL>
# @version:
import asyncio
import json
import sys
from collections import deque
from urllib.parse import quote_plus
import redis.asyncio as redis
from redis.asyncio import ConnectionPool

from core.db import *
from core.handler.task import get_task_data
from core.util import *
import socket
import re
from bson import ObjectId
from motor.motor_asyncio import AsyncIOMotorCursor


class RedisManager:
    """Redis连接池管理器 - 单例模式实现高效连接复用"""

    def __init__(self):
        self.pool = None
        self._lock = asyncio.Lock()

    async def get_pool(self):
        """获取Redis连接池"""
        if self.pool is None:
            async with self._lock:
                # 双重检查锁定模式
                if self.pool is None:
                    try:
                        # 决策理由: 使用连接池配置优化Redis连接管理，减少连接建立/销毁开销
                        self.pool = ConnectionPool.from_url(
                            f"redis://:{quote_plus(REDIS_PASSWORD)}@{REDIS_IP}:{REDIS_PORT}",
                            max_connections=50,  # 最大连接数
                            retry_on_timeout=True,  # 超时重试
                            health_check_interval=30,  # 健康检查间隔30秒
                            socket_keepalive=True,  # 启用TCP keepalive
                            socket_keepalive_options={
                                socket.TCP_KEEPIDLE: 60,
                                socket.TCP_KEEPINTVL: 10,
                                socket.TCP_KEEPCNT: 10
                            } if hasattr(socket, 'TCP_KEEPIDLE') else {},
                            encoding="utf-8",
                            decode_responses=True
                        )
                        logger.info("Redis连接池初始化成功")
                    except Exception as e:
                        logger.error(f"Redis连接池初始化失败: {e}")
                        raise

        return redis.Redis(connection_pool=self.pool)

    async def close(self):
        """关闭连接池"""
        if self.pool:
            await self.pool.disconnect()
            self.pool = None
            logger.info("Redis连接池已关闭")


# 全局Redis管理器实例
redis_manager = RedisManager()


async def get_redis_pool():
    """获取Redis连接 - 使用连接池优化版本"""
    return await redis_manager.get_pool()


async def refresh_config(name, t, content=None):
    data = {
        "name": name,
        "type": t,
    }
    if content is not None:
        data['content'] = content

    redis_client = await get_redis_pool()
    try:
        name_all = []
        if name == "all":
            keys = await redis_client.keys("node:*")
            for key in keys:
                tmp_name = key.split(":")[1]
                name_all.append(tmp_name)
        else:
            name_all.append(name)
        for n in name_all:
            await redis_client.rpush(f"refresh_config:{n}", json.dumps(data))
    finally:
        # 连接池会自动管理连接，无需手动关闭
        pass


async def subscribe_log_channel():
    """Redis pub/sub日志订阅 - 优化版本，支持错误恢复和连接复用"""
    channel_name = 'logs'
    logger.info(f"Subscribed to channel {channel_name}")
    retry_count = 0
    max_retries = 5

    # 决策理由: 使用固定大小的deque替代列表，防止LOG_INFO内存泄漏
    MAX_LOG_ENTRIES = 1000

    while retry_count < max_retries:
        try:
            redis_client = await redis_manager.get_pool()
            async with redis_client.pubsub() as pubsub:
                await pubsub.psubscribe(channel_name)
                retry_count = 0  # 重置重试计数
                logger.info("Redis pub/sub连接已建立")

                while True:
                    message = await pubsub.get_message(
                        ignore_subscribe_messages=True,
                        timeout=10
                    )
                    if message is not None:
                        try:
                            data = json.loads(message["data"])
                            log_name = data["name"]

                            # 修复内存泄漏：使用固定大小的deque
                            if log_name in GET_LOG_NAME:
                                if log_name not in LOG_INFO:
                                    LOG_INFO[log_name] = deque(maxlen=MAX_LOG_ENTRIES)
                                LOG_INFO[log_name].append(data['log'])

                            if "Register Success" in data['log']:
                                await check_node_task(log_name, redis_client)

                            await redis_client.rpush(f'log:{log_name}', data['log'])
                            total_logs = await redis_client.llen(f'log:{log_name}')
                            if total_logs > TOTAL_LOGS:
                                await redis_client.delete(f'log:{log_name}')

                        except json.JSONDecodeError as e:
                            logger.error(f"JSON解析错误: {e}")
                        except Exception as e:
                            logger.error(f"处理消息时出错: {e}")

        except Exception as e:
            retry_count += 1
            logger.error(f"Pubsub连接错误 (尝试 {retry_count}/{max_retries}): {e}")
            if retry_count < max_retries:
                await asyncio.sleep(min(2 ** retry_count, 30))  # 指数退避
            else:
                logger.error("达到最大重试次数，停止重连")
                break


async def check_node_task(node_name, redis_conn):
    """检查节点任务 - 使用新的连接池管理，优化N+1查询问题"""
    mongo_client = await get_mongo_db()
    query = {
        "progress": {"$ne": 100},
        "status": 1,
        "$or": [
            {"node": node_name},
            {"allNode": True}
        ]
    }
    cursor: AsyncIOMotorCursor = mongo_client.task.find(query)
    result = await cursor.to_list(length=None)
    if len(result) == 0:
        return

    # 决策理由: 预先批量获取所有需要的模板数据，避免N+1查询问题
    # 收集所有唯一的模板ID
    template_ids = list(set(doc.get("template") for doc in result if doc.get("template")))

    # 批量获取所有模板数据
    template_cache = {}
    if template_ids:
        template_obj_ids = [ObjectId(tid) for tid in template_ids]
        template_cursor = mongo_client.ScanTemplates.find({"_id": {"$in": template_obj_ids}})
        async for template_doc in template_cursor:
            template_cache[str(template_doc["_id"])] = template_doc

    # 预先获取字典和端口数据（这些数据相对稳定，可以缓存）
    dict_cache = {}
    port_cache = {}

    # 获取字典数据
    dict_cursor = mongo_client["dictionary"].find({})
    async for doc in dict_cursor:
        dict_cache[f'{doc["category"].lower()}.{doc["name"].lower()}'] = str(doc['_id'])

    # 获取端口数据
    port_cursor = mongo_client.PortDict.find({})
    async for doc in port_cursor:
        port_cache[f'{doc["name"].lower()}'] = doc["value"]

    # 批量处理任务数据
    tasks = []
    for doc in result:
        template_data = await get_task_data_optimized(
            mongo_client, doc, str(doc["_id"]),
            template_cache, dict_cache, port_cache
        )
        task = redis_conn.rpush(f"NodeTask:{node_name}", json.dumps(template_data))
        tasks.append(task)

    # 并发执行所有Redis操作
    await asyncio.gather(*tasks)
    return


async def get_redis_online_data(redis_con=None):
    """获取在线节点数据 - 使用连接池优化版本"""
    if redis_con is None:
        redis_con = await get_redis_pool()

    # 获取所有以 node: 开头的键
    keys = await redis_con.keys("node:*")
    # 构建结果字典
    result = []
    for key in keys:
        name = key.split(":")[1]
        hash_data = await redis_con.hgetall(key)
        if hash_data.get('state') == '1':
            result.append(name)
    return result


async def get_task_data_optimized(db, request_data, task_id, template_cache, dict_cache, port_cache):
    """优化版本的get_task_data函数 - 避免N+1查询问题"""
    template_id = request_data.get("template")
    if not template_id or template_id not in template_cache:
        logger.error(f"Template {template_id} not found in cache")
        return None

    # 从缓存获取模板数据
    template_data = template_cache[template_id].copy()

    # 如果选择了poc 将poc参数拼接到nuclei的参数中
    if len(template_data.get('vullist', [])) != 0:
        vul_tmp = ""
        if "All Poc" in template_data['vullist']:
            vul_tmp = "*"
        else:
            for vul in template_data['vullist']:
                vul_tmp += vul + ".yaml" + ","
        vul_tmp = vul_tmp.strip(",")

        if "VulnerabilityScan" not in template_data["Parameters"]:
            template_data["Parameters"]["VulnerabilityScan"] = {"ed93b8af6b72fe54a60efdb932cf6fbc": ""}
        if "ed93b8af6b72fe54a60efdb932cf6fbc" not in template_data["Parameters"]["VulnerabilityScan"]:
            template_data["Parameters"]["VulnerabilityScan"]["ed93b8af6b72fe54a60efdb932cf6fbc"] = ""

        if "ed93b8af6b72fe54a60efdb932cf6fbc" in template_data["VulnerabilityScan"]:
            template_data["Parameters"]["VulnerabilityScan"]["ed93b8af6b72fe54a60efdb932cf6fbc"] = \
                template_data["Parameters"]["VulnerabilityScan"][
                    "ed93b8af6b72fe54a60efdb932cf6fbc"] + " -t " + vul_tmp

    # 解析参数，使用缓存的字典和端口数据
    template_data["Parameters"] = await parameter_parser_optimized(template_data["Parameters"], dict_cache, port_cache)

    # 删除原始的vullist
    if 'vullist' in template_data:
        del template_data["vullist"]
    if '_id' in template_data:
        del template_data["_id"]

    # 设置任务信息
    template_data["TaskName"] = request_data.get("name", "")
    template_data["ignore"] = request_data.get("ignore", "")
    template_data["duplicates"] = request_data.get("duplicates", False)
    template_data["ID"] = str(task_id)
    template_data["type"] = request_data.get("type", "scan")
    template_data["IsStart"] = request_data.get("IsStart", False)

    return template_data


async def parameter_parser_optimized(parameter, dict_cache, port_cache):
    """优化版本的参数解析函数 - 使用预加载的缓存数据"""
    for module_name in parameter:
        for plugin in parameter[module_name]:
            matches = re.findall(r'\{(.*?)\}', parameter[module_name][plugin])
            for match in matches:
                try:
                    tp, value = match.split(".", 1)
                    if tp == "dict":
                        if value.lower() in dict_cache:
                            real_param = dict_cache[value.lower()]
                        else:
                            real_param = match
                            logger.error(f"parameter error:module {module_name} plugin {plugin} parameter {parameter[module_name][plugin]}")
                        parameter[module_name][plugin] = parameter[module_name][plugin].replace("{" + match + "}", real_param)
                    elif tp == "port":
                        if value.lower() in port_cache:
                            real_param = port_cache[value.lower()]
                        else:
                            real_param = match
                            logger.error(f"parameter error:module {module_name} plugin {plugin} parameter {parameter[module_name][plugin]}")
                        parameter[module_name][plugin] = parameter[module_name][plugin].replace("{" + match + "}", real_param)
                except ValueError:
                    logger.error(f"Invalid parameter format: {match}")
                    continue
    return parameter
